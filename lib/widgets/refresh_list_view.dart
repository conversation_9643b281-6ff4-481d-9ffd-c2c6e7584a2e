import 'package:flutter/material.dart';
import 'package:easy_refresh/easy_refresh.dart';
import 'package:get/get.dart';

/// 通用刷新加载列表组件
/// 配合GetX使用，支持下拉刷新和上拉加载更多
class RefreshListView<T> extends StatelessWidget {
  /// 控制器
  final RefreshListController<T> controller;

  /// 列表项构建器
  final Widget Function(BuildContext context, T item, int index) itemBuilder;

  /// 分隔符构建器
  final Widget Function(BuildContext context, int index)? separatorBuilder;

  /// 空数据占位符
  final Widget? emptyWidget;

  /// 错误占位符
  final Widget Function(String error, VoidCallback retry)? errorBuilder;

  /// 加载中占位符
  final Widget? loadingWidget;

  /// 列表内边距
  final EdgeInsetsGeometry? padding;

  /// 是否收缩包装
  final bool shrinkWrap;

  /// 滚动物理效果
  final ScrollPhysics? physics;

  /// 滚动控制器
  final ScrollController? scrollController;

  const RefreshListView({
    Key? key,
    required this.controller,
    required this.itemBuilder,
    this.separatorBuilder,
    this.emptyWidget,
    this.errorBuilder,
    this.loadingWidget,
    this.padding,
    this.shrinkWrap = false,
    this.physics,
    this.scrollController,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      // 首次加载中
      if (controller.isFirstLoading.value) {
        return loadingWidget ??
            const Center(
              child: CircularProgressIndicator(),
            );
      }

      // 加载错误
      if (controller.hasError.value) {
        return errorBuilder?.call(
              controller.errorMessage.value,
              controller.retry,
            ) ??
            _buildDefaultError();
      }

      // 空数据
      if (controller.isEmpty) {
        return emptyWidget ?? _buildDefaultEmpty();
      }

      // 列表内容
      return EasyRefresh(
        controller: controller.refreshController,
        header: const ClassicHeader(
          dragText: '下拉刷新',
          armedText: '释放刷新',
          readyText: '刷新中...',
          processingText: '刷新中...',
          processedText: '刷新完成',
          noMoreText: '没有更多',
          failedText: '刷新失败',
          messageText: '最后更新于 %T',
        ),
        footer: const ClassicFooter(
          dragText: '上拉加载',
          armedText: '释放加载',
          readyText: '加载中...',
          processingText: '加载中...',
          processedText: '加载完成',
          noMoreText: '没有更多数据',
          failedText: '加载失败',
          messageText: '最后更新于 %T',
        ),
        onRefresh: controller.onRefresh,
        onLoad: controller.hasMore.value ? controller.onLoadMore : null,
        child: ListView.separated(
          controller: scrollController,
          padding: padding,
          shrinkWrap: shrinkWrap,
          physics: physics,
          itemCount: controller.dataList.length,
          itemBuilder: (context, index) {
            return itemBuilder(context, controller.dataList[index], index);
          },
          separatorBuilder: separatorBuilder ??
              (context, index) {
                return const Divider(height: 1);
              },
        ),
      );
    });
  }

  /// 默认错误页面
  Widget _buildDefaultError() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.error_outline,
            size: 64,
            color: Colors.grey,
          ),
          const SizedBox(height: 16),
          Text(
            controller.errorMessage.value,
            style: const TextStyle(
              fontSize: 16,
              color: Colors.grey,
            ),
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: controller.retry,
            child: const Text('重试'),
          ),
        ],
      ),
    );
  }

  /// 默认空数据页面
  Widget _buildDefaultEmpty() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.inbox_outlined,
            size: 64,
            color: Colors.grey,
          ),
          SizedBox(height: 16),
          Text(
            '暂无数据',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey,
            ),
          ),
        ],
      ),
    );
  }
}

/// 刷新列表控制器
abstract class RefreshListController<T> extends GetxController {
  /// 刷新控制器
  late EasyRefreshController refreshController;

  /// 数据列表
  final RxList<T> dataList = <T>[].obs;

  /// 当前页码
  final RxInt currentPage = 1.obs;

  /// 每页数量
  int get pageSize => 20;

  /// 是否有更多数据
  final RxBool hasMore = true.obs;

  /// 是否首次加载中
  final RxBool isFirstLoading = true.obs;

  /// 是否刷新中
  final RxBool isRefreshing = false.obs;

  /// 是否加载更多中
  final RxBool isLoadingMore = false.obs;

  /// 是否有错误
  final RxBool hasError = false.obs;

  /// 错误信息
  final RxString errorMessage = ''.obs;

  /// 是否为空
  bool get isEmpty =>
      dataList.isEmpty && !isFirstLoading.value && !hasError.value;

  @override
  void onInit() {
    super.onInit();
    refreshController = EasyRefreshController(
      controlFinishRefresh: true,
      controlFinishLoad: true,
    );
    loadData();
  }

  @override
  void onClose() {
    refreshController.dispose();
    super.onClose();
  }

  /// 加载数据（抽象方法，子类实现）
  Future<List<T>> fetchData(int page, int size);

  /// 初始加载数据
  Future<void> loadData() async {
    try {
      isFirstLoading.value = true;
      hasError.value = false;

      final data = await fetchData(1, pageSize);

      dataList.clear();
      dataList.addAll(data);
      currentPage.value = 1;
      hasMore.value = data.length >= pageSize;
    } catch (e) {
      hasError.value = true;
      errorMessage.value = e.toString();
    } finally {
      isFirstLoading.value = false;
    }
  }

  /// 下拉刷新
  Future<void> onRefresh() async {
    try {
      isRefreshing.value = true;
      hasError.value = false;

      final data = await fetchData(1, pageSize);

      dataList.clear();
      dataList.addAll(data);
      currentPage.value = 1;
      hasMore.value = data.length >= pageSize;

      refreshController.finishRefresh(IndicatorResult.success);
    } catch (e) {
      refreshController.finishRefresh(IndicatorResult.fail);
      Get.snackbar('错误', e.toString());
    } finally {
      isRefreshing.value = false;
    }
  }

  /// 上拉加载更多
  Future<void> onLoadMore() async {
    if (!hasMore.value || isLoadingMore.value) {
      refreshController.finishLoad(IndicatorResult.noMore);
      return;
    }

    try {
      isLoadingMore.value = true;

      final nextPage = currentPage.value + 1;
      final data = await fetchData(nextPage, pageSize);

      if (data.isNotEmpty) {
        dataList.addAll(data);
        currentPage.value = nextPage;
        hasMore.value = data.length >= pageSize;
        refreshController.finishLoad(IndicatorResult.success);
      } else {
        hasMore.value = false;
        refreshController.finishLoad(IndicatorResult.noMore);
      }
    } catch (e) {
      refreshController.finishLoad(IndicatorResult.fail);
      Get.snackbar('错误', e.toString());
    } finally {
      isLoadingMore.value = false;
    }
  }

  /// 重试
  void retry() {
    loadData();
  }

  /// 刷新数据
  @override
  void refresh() {
    onRefresh();
  }
}
