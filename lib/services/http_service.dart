import 'dart:io';
import 'package:dio/dio.dart';
import 'package:dio_cache_interceptor/dio_cache_interceptor.dart';
import 'package:get/get.dart' as getx;
import '../utils/storage_util.dart';
import '../utils/platform_util.dart';

/// HTTP请求服务类
/// 封装Dio，提供网络请求功能，包含错误处理、Token拦截、缓存等功能
class HttpService extends getx.GetxService {
  static HttpService get to => getx.Get.find();

  late Dio _dio;
  late CacheStore _cacheStore;
  late CacheOptions _cacheOptions;

  /// 基础URL
  String get baseUrl => AppConfigUtil.apiBaseUrl;

  /// 请求超时时间（毫秒）
  int get connectTimeout => 15000;
  int get receiveTimeout => 15000;

  @override
  void onInit() {
    super.onInit();
    _initDio();
    _initCache();
    _setupInterceptors();
  }

  /// 初始化Dio
  void _initDio() {
    _dio = Dio(BaseOptions(
      baseUrl: baseUrl,
      connectTimeout: Duration(milliseconds: connectTimeout),
      receiveTimeout: Duration(milliseconds: receiveTimeout),
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    ));
  }

  /// 初始化缓存
  void _initCache() {
    _cacheStore = MemCacheStore();
    _cacheOptions = CacheOptions(
      store: _cacheStore,
      policy: CachePolicy.request,
      hitCacheOnErrorExcept: [401, 403],
      maxStale: const Duration(days: 7),
      priority: CachePriority.normal,
      cipher: null,
      keyBuilder: CacheOptions.defaultCacheKeyBuilder,
      allowPostMethod: false,
    );
  }

  /// 设置拦截器
  void _setupInterceptors() {
    // 请求拦截器
    _dio.interceptors.add(InterceptorsWrapper(
      onRequest: (options, handler) {
        // 添加Token
        String? token = StorageUtil.getString(StorageKeys.token);
        if (token != null && token.isNotEmpty) {
          options.headers['Authorization'] = 'Bearer $token';
        }

        if (AppConfigUtil.enableLogging) {
          print('请求: ${options.method} ${options.uri}');
          print('请求头: ${options.headers}');
          print('请求参数: ${options.data}');
        }

        handler.next(options);
      },
      onResponse: (response, handler) {
        if (AppConfigUtil.enableLogging) {
          print('响应: ${response.statusCode} ${response.requestOptions.uri}');
          print('响应数据: ${response.data}');
        }
        handler.next(response);
      },
      onError: (error, handler) {
        if (AppConfigUtil.enableLogging) {
          print('请求错误: ${error.message}');
        }
        _handleError(error);
        handler.next(error);
      },
    ));

    // 缓存拦截器
    _dio.interceptors.add(DioCacheInterceptor(options: _cacheOptions));

    // Token过期拦截器
    _dio.interceptors.add(InterceptorsWrapper(
      onError: (error, handler) async {
        if (error.response?.statusCode == 401) {
          await _handleTokenExpired();
        }
        handler.next(error);
      },
    ));
  }

  /// 处理Token过期
  Future<void> _handleTokenExpired() async {
    // 清除本地Token
    await StorageUtil.remove(StorageKeys.token);
    await StorageUtil.remove(StorageKeys.userInfo);

    // 跳转到登录页面
    getx.Get.offAllNamed('/login');

    // 显示提示
    getx.Get.snackbar(
      '提示',
      '登录已过期，请重新登录',
      snackPosition: getx.SnackPosition.TOP,
    );
  }

  /// 处理网络错误
  void _handleError(DioException error) {
    String message = '网络请求失败';

    switch (error.type) {
      case DioExceptionType.connectionTimeout:
        message = '连接超时';
        break;
      case DioExceptionType.sendTimeout:
        message = '发送超时';
        break;
      case DioExceptionType.receiveTimeout:
        message = '接收超时';
        break;
      case DioExceptionType.badResponse:
        message = _getErrorMessage(error.response);
        break;
      case DioExceptionType.cancel:
        message = '请求已取消';
        break;
      case DioExceptionType.unknown:
        if (error.error is SocketException) {
          message = '网络连接失败，请检查网络设置';
        } else {
          message = '未知错误: ${error.message}';
        }
        break;
      default:
        message = '网络请求失败: ${error.message}';
    }

    // 显示错误提示
    getx.Get.snackbar(
      '错误',
      message,
      snackPosition: getx.SnackPosition.TOP,
      backgroundColor: getx.Get.theme.colorScheme.error,
      colorText: getx.Get.theme.colorScheme.onError,
    );
  }

  /// 获取错误信息
  String _getErrorMessage(Response? response) {
    if (response == null) return '服务器响应异常';

    try {
      final data = response.data;
      if (data is Map<String, dynamic>) {
        return data['message'] ?? data['msg'] ?? '服务器错误';
      }
      return '服务器错误 (${response.statusCode})';
    } catch (e) {
      return '服务器错误 (${response.statusCode})';
    }
  }

  /// GET请求
  Future<Response<T>> get<T>(
    String path, {
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    bool useCache = true,
  }) async {
    Options requestOptions = options ?? Options();

    if (useCache) {
      requestOptions = requestOptions.copyWith(
        extra: {
          ...requestOptions.extra ?? {},
          ..._cacheOptions.toExtra(),
        },
      );
    }

    return await _dio.get<T>(
      path,
      queryParameters: queryParameters,
      options: requestOptions,
      cancelToken: cancelToken,
    );
  }

  /// POST请求
  Future<Response<T>> post<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    bool useCache = false,
  }) async {
    Options requestOptions = options ?? Options();

    if (useCache) {
      requestOptions = requestOptions.copyWith(
        extra: {
          ...requestOptions.extra ?? {},
          ..._cacheOptions.toExtra(),
        },
      );
    }

    return await _dio.post<T>(
      path,
      data: data,
      queryParameters: queryParameters,
      options: requestOptions,
      cancelToken: cancelToken,
    );
  }

  /// PUT请求
  Future<Response<T>> put<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
  }) async {
    return await _dio.put<T>(
      path,
      data: data,
      queryParameters: queryParameters,
      options: options,
      cancelToken: cancelToken,
    );
  }

  /// DELETE请求
  Future<Response<T>> delete<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
  }) async {
    return await _dio.delete<T>(
      path,
      data: data,
      queryParameters: queryParameters,
      options: options,
      cancelToken: cancelToken,
    );
  }

  /// 上传文件
  Future<Response<T>> upload<T>(
    String path,
    String filePath, {
    String? fileName,
    Map<String, dynamic>? data,
    ProgressCallback? onSendProgress,
    CancelToken? cancelToken,
  }) async {
    FormData formData = FormData.fromMap({
      ...?data,
      'file': await MultipartFile.fromFile(
        filePath,
        filename: fileName,
      ),
    });

    return await _dio.post<T>(
      path,
      data: formData,
      onSendProgress: onSendProgress,
      cancelToken: cancelToken,
    );
  }

  /// 下载文件
  Future<Response> download(
    String urlPath,
    String savePath, {
    ProgressCallback? onReceiveProgress,
    CancelToken? cancelToken,
    bool deleteOnError = true,
    String lengthHeader = Headers.contentLengthHeader,
    Map<String, dynamic>? queryParameters,
    Options? options,
  }) async {
    return await _dio.download(
      urlPath,
      savePath,
      onReceiveProgress: onReceiveProgress,
      cancelToken: cancelToken,
      deleteOnError: deleteOnError,
      lengthHeader: lengthHeader,
      queryParameters: queryParameters,
      options: options,
    );
  }

  /// 清除缓存
  Future<void> clearCache() async {
    await _cacheStore.clean();
  }

  /// 清除指定URL的缓存
  Future<void> clearCacheForUrl(String url) async {
    final key = CacheOptions.defaultCacheKeyBuilder(
      RequestOptions(path: url),
    );
    await _cacheStore.delete(key);
  }
}
