import 'package:get/get.dart';
import 'app_routes.dart';
import '../pages/home/<USER>';
import '../pages/home/<USER>';
import '../pages/login/login_page.dart';
import '../pages/login/login_binding.dart';
import '../pages/example_list/example_list_page.dart';
import '../pages/example_list/example_list_binding.dart';
import '../pages/not_found/not_found_page.dart';

/// 应用页面配置
class AppPages {
  /// 初始路由
  static const String initial = AppRoutes.initial;

  /// 所有页面路由
  static final routes = [
    // 首页
    GetPage(
      name: AppRoutes.home,
      page: () => const HomePage(),
      binding: HomeBinding(),
    ),
    
    // 登录页
    GetPage(
      name: AppRoutes.login,
      page: () => const LoginPage(),
      binding: LoginBinding(),
    ),
    
    // 示例列表页
    GetPage(
      name: AppRoutes.exampleList,
      page: () => const ExampleListPage(),
      binding: ExampleListBinding(),
    ),
    
    // 404页面
    GetPage(
      name: AppRoutes.notFound,
      page: () => const NotFoundPage(),
    ),
  ];

  /// 未知路由处理
  static GetPage get unknownRoute => GetPage(
    name: AppRoutes.notFound,
    page: () => const NotFoundPage(),
  );
}
