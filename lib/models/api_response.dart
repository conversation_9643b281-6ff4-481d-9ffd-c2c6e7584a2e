/// API响应基础模型
class ApiResponse<T> {
  final int code;
  final String message;
  final T? data;
  final bool success;

  ApiResponse({
    required this.code,
    required this.message,
    this.data,
    required this.success,
  });

  factory ApiResponse.fromJson(
    Map<String, dynamic> json,
    T Function(dynamic)? fromJsonT,
  ) {
    return ApiResponse<T>(
      code: json['code'] ?? json['status'] ?? 0,
      message: json['message'] ?? json['msg'] ?? '',
      data: json['data'] != null && fromJsonT != null 
          ? fromJsonT(json['data']) 
          : json['data'],
      success: (json['code'] ?? json['status'] ?? 0) == 200 || 
               (json['success'] ?? false),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'code': code,
      'message': message,
      'data': data,
      'success': success,
    };
  }

  /// 是否成功
  bool get isSuccess => success && code == 200;

  /// 是否失败
  bool get isFailure => !isSuccess;

  @override
  String toString() {
    return 'ApiResponse{code: $code, message: $message, data: $data, success: $success}';
  }
}

/// 分页响应模型
class PageResponse<T> {
  final List<T> list;
  final int total;
  final int page;
  final int pageSize;
  final int totalPages;
  final bool hasNext;
  final bool hasPrev;

  PageResponse({
    required this.list,
    required this.total,
    required this.page,
    required this.pageSize,
    required this.totalPages,
    required this.hasNext,
    required this.hasPrev,
  });

  factory PageResponse.fromJson(
    Map<String, dynamic> json,
    T Function(Map<String, dynamic>) fromJsonT,
  ) {
    final list = (json['list'] ?? json['data'] ?? json['records'] ?? []) as List;
    final total = json['total'] ?? json['totalCount'] ?? 0;
    final page = json['page'] ?? json['current'] ?? 1;
    final pageSize = json['pageSize'] ?? json['size'] ?? 10;
    final totalPages = (total / pageSize).ceil();

    return PageResponse<T>(
      list: list.map((item) => fromJsonT(item as Map<String, dynamic>)).toList(),
      total: total,
      page: page,
      pageSize: pageSize,
      totalPages: totalPages,
      hasNext: page < totalPages,
      hasPrev: page > 1,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'list': list,
      'total': total,
      'page': page,
      'pageSize': pageSize,
      'totalPages': totalPages,
      'hasNext': hasNext,
      'hasPrev': hasPrev,
    };
  }

  @override
  String toString() {
    return 'PageResponse{total: $total, page: $page, pageSize: $pageSize, listLength: ${list.length}}';
  }
}

/// 用户信息模型示例
class UserInfo {
  final int id;
  final String username;
  final String nickname;
  final String? avatar;
  final String? email;
  final String? phone;
  final DateTime? createTime;
  final DateTime? updateTime;

  UserInfo({
    required this.id,
    required this.username,
    required this.nickname,
    this.avatar,
    this.email,
    this.phone,
    this.createTime,
    this.updateTime,
  });

  factory UserInfo.fromJson(Map<String, dynamic> json) {
    return UserInfo(
      id: json['id'] ?? 0,
      username: json['username'] ?? '',
      nickname: json['nickname'] ?? '',
      avatar: json['avatar'],
      email: json['email'],
      phone: json['phone'],
      createTime: json['createTime'] != null 
          ? DateTime.tryParse(json['createTime']) 
          : null,
      updateTime: json['updateTime'] != null 
          ? DateTime.tryParse(json['updateTime']) 
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'username': username,
      'nickname': nickname,
      'avatar': avatar,
      'email': email,
      'phone': phone,
      'createTime': createTime?.toIso8601String(),
      'updateTime': updateTime?.toIso8601String(),
    };
  }

  @override
  String toString() {
    return 'UserInfo{id: $id, username: $username, nickname: $nickname}';
  }
}
