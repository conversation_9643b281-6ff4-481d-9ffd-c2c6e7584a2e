import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'routes/app_pages.dart';
import 'services/http_service.dart';
import 'utils/storage_util.dart';
import 'utils/platform_util.dart';

// UME调试工具相关导入 (已移除，如需要可重新添加)
import 'package:flutter_ume/flutter_ume.dart';
import 'package:flutter_ume_kit_ui/flutter_ume_kit_ui.dart';
import 'package:flutter_ume_kit_perf/flutter_ume_kit_perf.dart';
// import 'package:flutter_ume_kit_show_fps/flutter_ume_kit_show_fps.dart';
import 'package:flutter_ume_kit_device/flutter_ume_kit_device.dart';
import 'package:flutter_ume_kit_console/flutter_ume_kit_console.dart';
import 'package:flutter_ume_kit_dio/flutter_ume_kit_dio.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // 打印环境信息
  EnvironmentUtil.printEnvironmentInfo();

  // 初始化本地存储
  await StorageUtil.init();

  // 初始化服务
  await initServices();

  // TODO: 初始化UME调试工具 (等依赖安装完成后启用)
  if (AppConfigUtil.enableDebugTools) {
    PluginManager.instance
      ..register(WidgetInfoInspector())
      ..register(WidgetDetailInspector())
      ..register(ColorSucker())
      ..register(AlignRuler())
      ..register(Performance())
      ..register(MemoryInfoPage())
      ..register(CpuInfoPage())
      ..register(DeviceInfoPanel())
      ..register(Console())
      ..register(DioInspector(dio: HttpService.to._dio));
  }

  runApp(const MyApp());
}

/// 初始化服务
Future<void> initServices() async {
  // 注册HTTP服务
  Get.put(HttpService());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    Widget app = GetMaterialApp(
      title: 'Flutter Base Project',
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.blue),
        useMaterial3: true,
        appBarTheme: const AppBarTheme(
          centerTitle: true,
          elevation: 0,
        ),
      ),
      // GetX路由配置
      initialRoute: AppPages.initial,
      getPages: AppPages.routes,
      unknownRoute: AppPages.unknownRoute,

      // 调试配置
      debugShowCheckedModeBanner: false,

      // 默认过渡动画
      defaultTransition: Transition.cupertino,
      transitionDuration: const Duration(milliseconds: 300),

      // 国际化配置
      locale: const Locale('zh', 'CN'),
      fallbackLocale: const Locale('en', 'US'),
    );

    // TODO: 包装UME调试工具 (等依赖安装完成后启用)
    // if (AppConfigUtil.enableDebugTools) {
    //   app = UMEWidget(child: app);
    // }

    return app;
  }
}
