import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'home_controller.dart';

/// 首页
class HomePage extends GetView<HomeController> {
  const HomePage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Flutter Base Project'),
        actions: [
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () {
              // 跳转到设置页面
              Get.toNamed('/settings');
            },
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // 欢迎卡片
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '欢迎使用 Flutter Base Project',
                      style: Theme.of(context).textTheme.headlineSmall,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      '这是一个基于GetX的Flutter项目基础框架',
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 24),
            
            // 功能按钮
            Text(
              '功能演示',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),
            
            // 示例列表按钮
            ElevatedButton.icon(
              onPressed: () {
                Get.toNamed('/example-list');
              },
              icon: const Icon(Icons.list),
              label: const Text('刷新加载列表示例'),
            ),
            
            const SizedBox(height: 12),
            
            // 登录按钮
            ElevatedButton.icon(
              onPressed: () {
                Get.toNamed('/login');
              },
              icon: const Icon(Icons.login),
              label: const Text('登录页面'),
            ),
            
            const SizedBox(height: 12),
            
            // 网络请求测试按钮
            Obx(() => ElevatedButton.icon(
              onPressed: controller.isLoading.value ? null : controller.testNetworkRequest,
              icon: controller.isLoading.value 
                  ? const SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                  : const Icon(Icons.cloud),
              label: Text(controller.isLoading.value ? '请求中...' : '测试网络请求'),
            )),
            
            const SizedBox(height: 12),
            
            // 本地存储测试按钮
            ElevatedButton.icon(
              onPressed: controller.testLocalStorage,
              icon: const Icon(Icons.storage),
              label: const Text('测试本地存储'),
            ),
            
            const SizedBox(height: 24),
            
            // 状态显示
            Obx(() => Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '状态信息',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 8),
                    Text('计数器: ${controller.counter.value}'),
                    const SizedBox(height: 4),
                    Text('最后操作: ${controller.lastAction.value}'),
                  ],
                ),
              ),
            )),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: controller.increment,
        tooltip: '增加计数',
        child: const Icon(Icons.add),
      ),
    );
  }
}
