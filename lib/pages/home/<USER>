import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../utils/storage_util.dart';

/// 首页控制器
class HomeController extends GetxController {
  /// 计数器
  final RxInt counter = 0.obs;

  /// 最后操作
  final RxString lastAction = '无'.obs;

  /// 是否加载中
  final RxBool isLoading = false.obs;

  @override
  void onInit() {
    super.onInit();
    _loadCounter();
  }

  /// 加载计数器
  void _loadCounter() {
    counter.value = StorageUtil.getInt('counter', defaultValue: 0) ?? 0;
  }

  /// 增加计数
  void increment() {
    counter.value++;
    lastAction.value = '增加计数';
    _saveCounter();
  }

  /// 保存计数器
  void _saveCounter() {
    StorageUtil.setInt('counter', counter.value);
  }

  /// 测试网络请求
  Future<void> testNetworkRequest() async {
    try {
      isLoading.value = true;
      lastAction.value = '开始网络请求';

      // 模拟网络请求
      await Future.delayed(const Duration(seconds: 2));

      // 这里可以调用真实的API
      // final response = await HttpService.to.get('/api/test');

      lastAction.value = '网络请求成功';
      Get.snackbar(
        '成功',
        '网络请求测试成功',
        snackPosition: SnackPosition.TOP,
      );
    } catch (e) {
      lastAction.value = '网络请求失败';
      Get.snackbar(
        '错误',
        '网络请求失败: $e',
        snackPosition: SnackPosition.TOP,
      );
    } finally {
      isLoading.value = false;
    }
  }

  /// 测试本地存储
  void testLocalStorage() {
    try {
      // 存储测试数据
      StorageUtil.setString('test_key', 'Hello Flutter Base Project!');
      StorageUtil.setBool('test_bool', true);
      StorageUtil.setDouble('test_double', 3.14);

      // 读取测试数据
      final testString = StorageUtil.getString('test_key');
      final testBool = StorageUtil.getBool('test_bool');
      final testDouble = StorageUtil.getDouble('test_double');

      lastAction.value = '本地存储测试完成';

      Get.dialog(
        AlertDialog(
          title: const Text('本地存储测试结果'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('字符串: $testString'),
              Text('布尔值: $testBool'),
              Text('浮点数: $testDouble'),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Get.back(),
              child: const Text('确定'),
            ),
          ],
        ),
      );
    } catch (e) {
      lastAction.value = '本地存储测试失败';
      Get.snackbar(
        '错误',
        '本地存储测试失败: $e',
        snackPosition: SnackPosition.TOP,
      );
    }
  }
}
