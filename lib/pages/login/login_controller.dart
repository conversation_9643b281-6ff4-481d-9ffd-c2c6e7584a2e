import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../utils/storage_util.dart';
import '../../models/api_response.dart';

/// 登录控制器
class LoginController extends GetxController {
  /// 用户名控制器
  final TextEditingController usernameController = TextEditingController();

  /// 密码控制器
  final TextEditingController passwordController = TextEditingController();

  /// 是否隐藏密码
  final RxBool obscurePassword = true.obs;

  /// 是否加载中
  final RxBool isLoading = false.obs;

  @override
  void onInit() {
    super.onInit();
    // 预填充用户名（如果有保存的话）
    final savedUsername = StorageUtil.getString('saved_username');
    if (savedUsername != null) {
      usernameController.text = savedUsername;
    }
  }

  @override
  void onClose() {
    usernameController.dispose();
    passwordController.dispose();
    super.onClose();
  }

  /// 切换密码可见性
  void togglePasswordVisibility() {
    obscurePassword.value = !obscurePassword.value;
  }

  /// 登录
  Future<void> login() async {
    final username = usernameController.text.trim();
    final password = passwordController.text.trim();

    // 验证输入
    if (username.isEmpty) {
      Get.snackbar('错误', '请输入用户名');
      return;
    }

    if (password.isEmpty) {
      Get.snackbar('错误', '请输入密码');
      return;
    }

    try {
      isLoading.value = true;

      // 模拟登录请求
      await _mockLogin(username, password);

      // 真实的登录请求示例：
      // final response = await HttpService.to.post('/api/login', data: {
      //   'username': username,
      //   'password': password,
      // });
      //
      // final apiResponse = ApiResponse<Map<String, dynamic>>.fromJson(
      //   response.data,
      //   (data) => data as Map<String, dynamic>,
      // );
      //
      // if (apiResponse.isSuccess) {
      //   final token = apiResponse.data?['token'];
      //   final userInfo = UserInfo.fromJson(apiResponse.data?['userInfo']);
      //
      //   // 保存登录信息
      //   await StorageUtil.setString(StorageKeys.token, token);
      //   await StorageUtil.setObject(StorageKeys.userInfo, userInfo.toJson());
      //
      //   Get.snackbar('成功', '登录成功');
      //   Get.offAllNamed('/home');
      // } else {
      //   Get.snackbar('错误', apiResponse.message);
      // }
    } catch (e) {
      Get.snackbar('错误', '登录失败: $e');
    } finally {
      isLoading.value = false;
    }
  }

  /// 模拟登录
  Future<void> _mockLogin(String username, String password) async {
    // 模拟网络延迟
    await Future.delayed(const Duration(seconds: 2));

    // 模拟登录验证
    if (username == 'admin' && password == '123456') {
      // 保存登录信息
      await StorageUtil.setString(StorageKeys.token, 'mock_token_12345');
      await StorageUtil.setString('saved_username', username);

      // 模拟用户信息
      final userInfo = UserInfo(
        id: 1,
        username: username,
        nickname: '管理员',
        avatar: null,
        email: '<EMAIL>',
        phone: null,
        createTime: DateTime.now(),
        updateTime: DateTime.now(),
      );

      await StorageUtil.setObject(StorageKeys.userInfo, userInfo.toJson());

      Get.snackbar('成功', '登录成功');
      Get.offAllNamed('/home');
    } else {
      throw Exception('用户名或密码错误');
    }
  }
}
