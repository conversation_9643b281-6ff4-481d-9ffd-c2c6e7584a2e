import 'dart:math';
import 'package:get/get.dart';
import '../../widgets/refresh_list_view.dart';

/// 示例项目模型
class ExampleItem {
  final int id;
  final String title;
  final String description;
  final bool isCompleted;
  final DateTime createTime;

  ExampleItem({
    required this.id,
    required this.title,
    required this.description,
    required this.isCompleted,
    required this.createTime,
  });

  ExampleItem copyWith({
    int? id,
    String? title,
    String? description,
    bool? isCompleted,
    DateTime? createTime,
  }) {
    return ExampleItem(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      isCompleted: isCompleted ?? this.isCompleted,
      createTime: createTime ?? this.createTime,
    );
  }
}

/// 示例列表控制器
class ExampleListController extends RefreshListController<ExampleItem> {
  final Random _random = Random();

  @override
  Future<List<ExampleItem>> fetchData(int page, int size) async {
    // 模拟网络延迟
    await Future.delayed(Duration(milliseconds: 500 + _random.nextInt(1000)));

    // 模拟偶尔的网络错误
    if (_random.nextDouble() < 0.1) {
      throw Exception('网络连接失败，请检查网络设置');
    }

    // 生成模拟数据
    final List<ExampleItem> items = [];
    final startId = (page - 1) * size + 1;
    
    for (int i = 0; i < size; i++) {
      final id = startId + i;
      
      // 模拟最后一页数据较少
      if (page > 3 && i > size ~/ 2) {
        break;
      }
      
      items.add(ExampleItem(
        id: id,
        title: '示例项目 $id',
        description: _generateDescription(id),
        isCompleted: _random.nextBool(),
        createTime: DateTime.now().subtract(Duration(days: _random.nextInt(30))),
      ));
    }

    return items;
  }

  /// 生成描述
  String _generateDescription(int id) {
    final descriptions = [
      '这是一个示例项目的描述信息',
      '用于演示刷新加载功能的测试数据',
      '支持下拉刷新和上拉加载更多',
      '集成了GetX状态管理',
      '提供了完整的错误处理机制',
      '支持空数据状态显示',
      '具有良好的用户体验',
      '代码结构清晰易维护',
    ];
    
    return descriptions[id % descriptions.length];
  }

  /// 切换项目状态
  void toggleItemStatus(ExampleItem item) {
    final index = dataList.indexWhere((element) => element.id == item.id);
    if (index != -1) {
      dataList[index] = item.copyWith(isCompleted: !item.isCompleted);
      Get.snackbar(
        '成功',
        '已${item.isCompleted ? "取消完成" : "标记完成"}: ${item.title}',
        snackPosition: SnackPosition.TOP,
      );
    }
  }
}
