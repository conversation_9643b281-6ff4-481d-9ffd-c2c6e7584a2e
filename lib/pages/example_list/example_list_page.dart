import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../widgets/refresh_list_view.dart';
import 'example_list_controller.dart';

/// 示例列表页面
class ExampleListPage extends GetView<ExampleListController> {
  const ExampleListPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('刷新加载列表示例'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: controller.refresh,
          ),
        ],
      ),
      body: RefreshListView<ExampleItem>(
        controller: controller,
        itemBuilder: (context, item, index) {
          return ListTile(
            leading: CircleAvatar(
              backgroundColor: Theme.of(context).primaryColor,
              child: Text(
                '${item.id}',
                style: const TextStyle(color: Colors.white),
              ),
            ),
            title: Text(item.title),
            subtitle: Text(item.description),
            trailing: Icon(
              item.isCompleted ? Icons.check_circle : Icons.radio_button_unchecked,
              color: item.isCompleted ? Colors.green : Colors.grey,
            ),
            onTap: () {
              _showItemDetail(context, item);
            },
          );
        },
        separatorBuilder: (context, index) {
          return const Divider(height: 1, indent: 72);
        },
        emptyWidget: const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.list_alt,
                size: 64,
                color: Colors.grey,
              ),
              SizedBox(height: 16),
              Text(
                '暂无数据',
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.grey,
                ),
              ),
              SizedBox(height: 8),
              Text(
                '下拉刷新试试',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey,
                ),
              ),
            ],
          ),
        ),
        errorBuilder: (error, retry) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(
                  Icons.error_outline,
                  size: 64,
                  color: Colors.red,
                ),
                const SizedBox(height: 16),
                Text(
                  '加载失败',
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.red[700],
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  error,
                  style: const TextStyle(
                    fontSize: 14,
                    color: Colors.grey,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: retry,
                  child: const Text('重试'),
                ),
              ],
            ),
          );
        },
        loadingWidget: const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircularProgressIndicator(),
              SizedBox(height: 16),
              Text(
                '加载中...',
                style: TextStyle(color: Colors.grey),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 显示项目详情
  void _showItemDetail(BuildContext context, ExampleItem item) {
    Get.dialog(
      AlertDialog(
        title: Text(item.title),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('ID: ${item.id}'),
            const SizedBox(height: 8),
            Text('描述: ${item.description}'),
            const SizedBox(height: 8),
            Text('状态: ${item.isCompleted ? "已完成" : "未完成"}'),
            const SizedBox(height: 8),
            Text('创建时间: ${item.createTime.toString().substring(0, 19)}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('关闭'),
          ),
          if (!item.isCompleted)
            TextButton(
              onPressed: () {
                controller.toggleItemStatus(item);
                Get.back();
              },
              child: const Text('标记完成'),
            ),
        ],
      ),
    );
  }
}
