import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'platform_util.dart';

/// 调试工具类
class DebugUtil {
  DebugUtil._();

  /// 是否启用调试模式
  static bool get isDebugEnabled => AppConfigUtil.enableDebugTools;

  /// 打印调试信息
  static void debugPrint(String message, {String? tag}) {
    if (isDebugEnabled) {
      final tagStr = tag != null ? '[$tag] ' : '';
      debugPrint('🐛 $tagStr$message');
    }
  }

  /// 打印网络请求信息
  static void logRequest(String method, String url, {Map<String, dynamic>? data}) {
    if (isDebugEnabled) {
      debugPrint('🌐 $method $url');
      if (data != null) {
        debugPrint('📤 Request Data: $data');
      }
    }
  }

  /// 打印网络响应信息
  static void logResponse(int statusCode, String url, {dynamic data}) {
    if (isDebugEnabled) {
      debugPrint('🌐 Response $statusCode $url');
      if (data != null) {
        debugPrint('📥 Response Data: $data');
      }
    }
  }

  /// 打印错误信息
  static void logError(String error, {String? tag, StackTrace? stackTrace}) {
    if (isDebugEnabled) {
      final tagStr = tag != null ? '[$tag] ' : '';
      debugPrint('❌ ${tagStr}Error: $error');
      if (stackTrace != null) {
        debugPrint('📍 StackTrace: $stackTrace');
      }
    }
  }

  /// 打印性能信息
  static void logPerformance(String operation, Duration duration) {
    if (isDebugEnabled) {
      debugPrint('⏱️ Performance: $operation took ${duration.inMilliseconds}ms');
    }
  }

  /// 显示调试信息对话框
  static void showDebugDialog(BuildContext context) {
    if (!isDebugEnabled) return;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('调试信息'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildInfoRow('平台', PlatformUtil.platformName),
              _buildInfoRow('运行模式', EnvironmentUtil.currentMode),
              _buildInfoRow('环境', EnvironmentUtil.currentEnvironment),
              _buildInfoRow('是否移动端', PlatformUtil.isMobile.toString()),
              _buildInfoRow('是否桌面端', PlatformUtil.isDesktop.toString()),
              _buildInfoRow('是否Web', PlatformUtil.isWeb.toString()),
              if (!kIsWeb) ...[
                _buildInfoRow('操作系统版本', PlatformUtil.operatingSystemVersion),
                _buildInfoRow('处理器数量', PlatformUtil.numberOfProcessors.toString()),
              ],
              _buildInfoRow('API地址', AppConfigUtil.apiBaseUrl),
              _buildInfoRow('启用日志', AppConfigUtil.enableLogging.toString()),
              _buildInfoRow('启用调试工具', AppConfigUtil.enableDebugTools.toString()),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('关闭'),
          ),
        ],
      ),
    );
  }

  /// 构建信息行
  static Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }

  /// 创建调试浮动按钮
  static Widget? createDebugFab(BuildContext context) {
    if (!isDebugEnabled) return null;

    return FloatingActionButton(
      mini: true,
      backgroundColor: Colors.red.withOpacity(0.7),
      onPressed: () => showDebugDialog(context),
      child: const Icon(Icons.bug_report, color: Colors.white),
    );
  }

  /// 性能监控装饰器
  static Future<T> measurePerformance<T>(
    String operation,
    Future<T> Function() function,
  ) async {
    if (!isDebugEnabled) {
      return await function();
    }

    final stopwatch = Stopwatch()..start();
    try {
      final result = await function();
      stopwatch.stop();
      logPerformance(operation, stopwatch.elapsed);
      return result;
    } catch (e) {
      stopwatch.stop();
      logError('Performance measurement failed for $operation: $e');
      rethrow;
    }
  }

  /// 内存使用情况（仅调试模式）
  static void logMemoryUsage() {
    if (isDebugEnabled && !kIsWeb) {
      // 这里可以添加内存使用情况的监控
      debugPrint('💾 Memory usage monitoring (placeholder)');
    }
  }

  /// 网络状态监控
  static void logNetworkStatus(bool isConnected) {
    if (isDebugEnabled) {
      debugPrint('📶 Network status: ${isConnected ? "Connected" : "Disconnected"}');
    }
  }

  /// 用户行为追踪（仅调试模式）
  static void logUserAction(String action, {Map<String, dynamic>? parameters}) {
    if (isDebugEnabled) {
      debugPrint('👤 User Action: $action');
      if (parameters != null) {
        debugPrint('📊 Parameters: $parameters');
      }
    }
  }

  /// 页面导航追踪
  static void logNavigation(String from, String to) {
    if (isDebugEnabled) {
      debugPrint('🧭 Navigation: $from → $to');
    }
  }

  /// 状态变化追踪
  static void logStateChange(String controller, String state, dynamic value) {
    if (isDebugEnabled) {
      debugPrint('🔄 State Change: [$controller] $state = $value');
    }
  }
}

/// 调试信息收集器
class DebugInfoCollector {
  static final List<String> _logs = [];
  static const int maxLogs = 1000;

  /// 添加日志
  static void addLog(String log) {
    if (DebugUtil.isDebugEnabled) {
      _logs.add('${DateTime.now().toIso8601String()}: $log');
      if (_logs.length > maxLogs) {
        _logs.removeAt(0);
      }
    }
  }

  /// 获取所有日志
  static List<String> getLogs() => List.unmodifiable(_logs);

  /// 清空日志
  static void clearLogs() => _logs.clear();

  /// 导出日志
  static String exportLogs() {
    return _logs.join('\n');
  }

  /// 显示日志查看器
  static void showLogViewer(BuildContext context) {
    if (!DebugUtil.isDebugEnabled) return;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('调试日志'),
        content: SizedBox(
          width: double.maxFinite,
          height: 400,
          child: ListView.builder(
            itemCount: _logs.length,
            itemBuilder: (context, index) {
              return Padding(
                padding: const EdgeInsets.symmetric(vertical: 2),
                child: Text(
                  _logs[index],
                  style: const TextStyle(fontSize: 12, fontFamily: 'monospace'),
                ),
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: clearLogs,
            child: const Text('清空'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('关闭'),
          ),
        ],
      ),
    );
  }
}
