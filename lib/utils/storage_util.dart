import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';

/// 本地存储工具类
/// 封装SharedPreferences，提供常用的存储方法
class StorageUtil {
  static SharedPreferences? _prefs;

  /// 初始化SharedPreferences
  static Future<void> init() async {
    _prefs = await SharedPreferences.getInstance();
  }

  /// 获取SharedPreferences实例
  static SharedPreferences get prefs {
    if (_prefs == null) {
      throw Exception('StorageUtil未初始化，请先调用StorageUtil.init()');
    }
    return _prefs!;
  }

  /// 存储字符串
  static Future<bool> setString(String key, String value) async {
    return await prefs.setString(key, value);
  }

  /// 获取字符串
  static String? getString(String key, {String? defaultValue}) {
    return prefs.getString(key) ?? defaultValue;
  }

  /// 存储整数
  static Future<bool> setInt(String key, int value) async {
    return await prefs.setInt(key, value);
  }

  /// 获取整数
  static int? getInt(String key, {int? defaultValue}) {
    return prefs.getInt(key) ?? defaultValue;
  }

  /// 存储双精度浮点数
  static Future<bool> setDouble(String key, double value) async {
    return await prefs.setDouble(key, value);
  }

  /// 获取双精度浮点数
  static double? getDouble(String key, {double? defaultValue}) {
    return prefs.getDouble(key) ?? defaultValue;
  }

  /// 存储布尔值
  static Future<bool> setBool(String key, bool value) async {
    return await prefs.setBool(key, value);
  }

  /// 获取布尔值
  static bool? getBool(String key, {bool? defaultValue}) {
    return prefs.getBool(key) ?? defaultValue;
  }

  /// 存储字符串列表
  static Future<bool> setStringList(String key, List<String> value) async {
    return await prefs.setStringList(key, value);
  }

  /// 获取字符串列表
  static List<String>? getStringList(String key, {List<String>? defaultValue}) {
    return prefs.getStringList(key) ?? defaultValue;
  }

  /// 存储对象（转换为JSON字符串）
  static Future<bool> setObject(String key, Object value) async {
    String jsonString = jsonEncode(value);
    return await setString(key, jsonString);
  }

  /// 获取对象（从JSON字符串解析）
  static T? getObject<T>(String key, T Function(Map<String, dynamic>) fromJson,
      {T? defaultValue}) {
    String? jsonString = getString(key);
    if (jsonString == null) return defaultValue;

    try {
      Map<String, dynamic> jsonMap = jsonDecode(jsonString);
      return fromJson(jsonMap);
    } catch (e) {
      print('解析JSON失败: $e');
      return defaultValue;
    }
  }

  /// 存储对象列表（转换为JSON字符串）
  static Future<bool> setObjectList<T>(String key, List<T> list) async {
    List<Map<String, dynamic>> jsonList = list.map((item) {
      if (item is Map<String, dynamic>) {
        return item;
      } else {
        // 假设对象有toJson方法
        return (item as dynamic).toJson() as Map<String, dynamic>;
      }
    }).toList();

    String jsonString = jsonEncode(jsonList);
    return await setString(key, jsonString);
  }

  /// 获取对象列表（从JSON字符串解析）
  static List<T>? getObjectList<T>(
      String key, T Function(Map<String, dynamic>) fromJson,
      {List<T>? defaultValue}) {
    String? jsonString = getString(key);
    if (jsonString == null) return defaultValue;

    try {
      List<dynamic> jsonList = jsonDecode(jsonString);
      return jsonList
          .map((item) => fromJson(item as Map<String, dynamic>))
          .toList();
    } catch (e) {
      print('解析JSON列表失败: $e');
      return defaultValue;
    }
  }

  /// 检查键是否存在
  static bool containsKey(String key) {
    return prefs.containsKey(key);
  }

  /// 删除指定键的数据
  static Future<bool> remove(String key) async {
    return await prefs.remove(key);
  }

  /// 清空所有数据
  static Future<bool> clear() async {
    return await prefs.clear();
  }

  /// 获取所有键
  static Set<String> getKeys() {
    return prefs.getKeys();
  }

  /// 重新加载数据（从磁盘重新读取）
  static Future<void> reload() async {
    await prefs.reload();
  }
}

/// 常用的存储键常量
class StorageKeys {
  static const String token = 'token';
  static const String userId = 'user_id';
  static const String userInfo = 'user_info';
  static const String isFirstLaunch = 'is_first_launch';
  static const String language = 'language';
  static const String theme = 'theme';
  static const String lastLoginTime = 'last_login_time';
}
