import 'dart:io';
import 'package:flutter/foundation.dart';

/// 平台和环境判断工具类
class PlatformUtil {
  PlatformUtil._();

  /// 是否为Android平台
  static bool get isAndroid => !kIsWeb && Platform.isAndroid;

  /// 是否为iOS平台
  static bool get isIOS => !kIsWeb && Platform.isIOS;

  /// 是否为Web平台
  static bool get isWeb => kIsWeb;

  /// 是否为Windows平台
  static bool get isWindows => !kIsWeb && Platform.isWindows;

  /// 是否为macOS平台
  static bool get isMacOS => !kIsWeb && Platform.isMacOS;

  /// 是否为Linux平台
  static bool get isLinux => !kIsWeb && Platform.isLinux;

  /// 是否为移动端平台
  static bool get isMobile => isAndroid || isIOS;

  /// 是否为桌面端平台
  static bool get isDesktop => isWindows || isMacOS || isLinux;

  /// 获取平台名称
  static String get platformName {
    if (isWeb) return 'Web';
    if (isAndroid) return 'Android';
    if (isIOS) return 'iOS';
    if (isWindows) return 'Windows';
    if (isMacOS) return 'macOS';
    if (isLinux) return 'Linux';
    return 'Unknown';
  }

  /// 获取操作系统版本
  static String get operatingSystemVersion {
    if (kIsWeb) return 'Web';
    return Platform.operatingSystemVersion;
  }

  /// 获取本地主机名
  static String get localHostname {
    if (kIsWeb) return 'Web';
    return Platform.localHostname;
  }

  /// 获取处理器数量
  static int get numberOfProcessors {
    if (kIsWeb) return 1;
    return Platform.numberOfProcessors;
  }

  /// 获取路径分隔符
  static String get pathSeparator {
    if (kIsWeb) return '/';
    return Platform.pathSeparator;
  }

  /// 获取环境变量
  static String? getEnvironmentVariable(String name) {
    if (kIsWeb) return null;
    return Platform.environment[name];
  }

  /// 获取可执行文件路径
  static String get resolvedExecutable {
    if (kIsWeb) return '';
    return Platform.resolvedExecutable;
  }

  /// 获取脚本路径
  static String get script {
    if (kIsWeb) return '';
    return Platform.script.toString();
  }
}

/// 环境判断工具类
class EnvironmentUtil {
  EnvironmentUtil._();

  /// 是否为调试模式
  static bool get isDebug => kDebugMode;

  /// 是否为发布模式
  static bool get isRelease => kReleaseMode;

  /// 是否为性能测试模式
  static bool get isProfile => kProfileMode;

  /// 获取当前运行模式
  static String get currentMode {
    if (isDebug) return 'Debug';
    if (isRelease) return 'Release';
    if (isProfile) return 'Profile';
    return 'Unknown';
  }

  /// 是否为开发环境
  /// 可以通过环境变量或编译时常量来判断
  static bool get isDevelopment {
    // 方式1: 通过编译时常量判断
    const environment = String.fromEnvironment('ENVIRONMENT', defaultValue: 'development');
    if (environment == 'development') return true;
    
    // 方式2: 通过调试模式判断
    if (isDebug) return true;
    
    // 方式3: 通过环境变量判断（仅非Web平台）
    if (!kIsWeb) {
      final env = Platform.environment['FLUTTER_ENV'];
      if (env == 'development' || env == 'dev') return true;
    }
    
    return false;
  }

  /// 是否为测试环境
  static bool get isTesting {
    // 方式1: 通过编译时常量判断
    const environment = String.fromEnvironment('ENVIRONMENT', defaultValue: 'development');
    if (environment == 'testing' || environment == 'test') return true;
    
    // 方式2: 通过环境变量判断（仅非Web平台）
    if (!kIsWeb) {
      final env = Platform.environment['FLUTTER_ENV'];
      if (env == 'testing' || env == 'test') return true;
    }
    
    return false;
  }

  /// 是否为生产环境
  static bool get isProduction {
    // 方式1: 通过编译时常量判断
    const environment = String.fromEnvironment('ENVIRONMENT', defaultValue: 'development');
    if (environment == 'production' || environment == 'prod') return true;
    
    // 方式2: 通过发布模式判断
    if (isRelease && !isDevelopment && !isTesting) return true;
    
    // 方式3: 通过环境变量判断（仅非Web平台）
    if (!kIsWeb) {
      final env = Platform.environment['FLUTTER_ENV'];
      if (env == 'production' || env == 'prod') return true;
    }
    
    return false;
  }

  /// 获取当前环境名称
  static String get currentEnvironment {
    if (isDevelopment) return 'Development';
    if (isTesting) return 'Testing';
    if (isProduction) return 'Production';
    return 'Unknown';
  }

  /// 获取环境配置
  static Map<String, dynamic> get environmentConfig {
    return {
      'platform': PlatformUtil.platformName,
      'mode': currentMode,
      'environment': currentEnvironment,
      'isDebug': isDebug,
      'isRelease': isRelease,
      'isProfile': isProfile,
      'isDevelopment': isDevelopment,
      'isTesting': isTesting,
      'isProduction': isProduction,
      'isMobile': PlatformUtil.isMobile,
      'isDesktop': PlatformUtil.isDesktop,
      'isWeb': PlatformUtil.isWeb,
    };
  }

  /// 打印环境信息
  static void printEnvironmentInfo() {
    if (isDebug) {
      print('=== 环境信息 ===');
      print('平台: ${PlatformUtil.platformName}');
      print('运行模式: $currentMode');
      print('环境: $currentEnvironment');
      print('是否移动端: ${PlatformUtil.isMobile}');
      print('是否桌面端: ${PlatformUtil.isDesktop}');
      print('是否Web: ${PlatformUtil.isWeb}');
      if (!kIsWeb) {
        print('操作系统版本: ${PlatformUtil.operatingSystemVersion}');
        print('处理器数量: ${PlatformUtil.numberOfProcessors}');
      }
      print('===============');
    }
  }
}

/// 应用配置工具类
class AppConfigUtil {
  AppConfigUtil._();

  /// 获取API基础URL
  static String get apiBaseUrl {
    if (EnvironmentUtil.isProduction) {
      return 'https://api.production.com';
    } else if (EnvironmentUtil.isTesting) {
      return 'https://api.testing.com';
    } else {
      return 'https://api.development.com';
    }
  }

  /// 获取应用名称
  static String get appName {
    if (EnvironmentUtil.isProduction) {
      return 'Flutter Base Project';
    } else if (EnvironmentUtil.isTesting) {
      return 'Flutter Base Project (Test)';
    } else {
      return 'Flutter Base Project (Dev)';
    }
  }

  /// 是否启用日志
  static bool get enableLogging {
    return EnvironmentUtil.isDevelopment || EnvironmentUtil.isTesting;
  }

  /// 是否启用调试工具
  static bool get enableDebugTools {
    return EnvironmentUtil.isDevelopment;
  }

  /// 获取日志级别
  static String get logLevel {
    if (EnvironmentUtil.isProduction) {
      return 'ERROR';
    } else if (EnvironmentUtil.isTesting) {
      return 'WARN';
    } else {
      return 'DEBUG';
    }
  }

  /// 获取完整配置
  static Map<String, dynamic> get config {
    return {
      'apiBaseUrl': apiBaseUrl,
      'appName': appName,
      'enableLogging': enableLogging,
      'enableDebugTools': enableDebugTools,
      'logLevel': logLevel,
      ...EnvironmentUtil.environmentConfig,
    };
  }
}
