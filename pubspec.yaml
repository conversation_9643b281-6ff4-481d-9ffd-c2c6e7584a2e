name: base_project
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.5.3

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8

  # 状态管理和路由
  get: ^4.6.6

  # 网络请求
  dio: ^4.0.0
  dio_cache_interceptor: ^3.5.0

  # 下拉刷新
  easy_refresh: ^3.3.4

  # 本地存储
  shared_preferences: ^2.2.2

dependency_overrides: 
  vm_service: ^7.1.1
  image: ^3.0.5
  dio: ^4.0.0

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^4.0.0

  # 调试工具
  flutter_ume: ^1.1.2
  flutter_ume_kit_ui: ^1.1.0
  flutter_ume_kit_device: ^1.0.0
  flutter_ume_kit_perf: ^1.0.0
  flutter_ume_kit_show_code: ^1.0.0+1
  flutter_ume_kit_console: ^1.0.0
  flutter_ume_kit_dio: ^1.0.1+2

  # 启动图
  flutter_native_splash: ^2.3.10

  # 应用图标
  flutter_launcher_icons: ^0.13.1

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/
    - assets/icons/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package

# Flutter Native Splash 配置
# 注意: 需要先将SVG文件转换为PNG格式，然后取消注释以下配置
# flutter_native_splash:
#   color: "#ffffff"
#   image: assets/images/splash_logo.png
#   color_dark: "#000000"
#   image_dark: assets/images/splash_logo_dark.png
#   android_12:
#     image: assets/images/splash_logo.png
#     icon_background_color: "#ffffff"
#     image_dark: assets/images/splash_logo_dark.png
#     icon_background_color_dark: "#000000"
#   web: false

# Flutter Launcher Icons 配置
# 注意: 需要先将SVG文件转换为PNG格式，然后取消注释以下配置
# flutter_launcher_icons:
#   android: "launcher_icon"
#   ios: true
#   image_path: "assets/icons/app_icon.png"
#   min_sdk_android: 21
#   web:
#     generate: true
#     image_path: "assets/icons/app_icon.png"
#     background_color: "#2196F3"
#     theme_color: "#1976D2"
#   windows:
#     generate: true
#     image_path: "assets/icons/app_icon.png"
#     icon_size: 48
#   macos:
#     generate: true
#     image_path: "assets/icons/app_icon.png"
