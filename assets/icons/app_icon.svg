<?xml version="1.0" encoding="UTF-8"?>
<svg width="1024" height="1024" viewBox="0 0 1024 1024" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景渐变 -->
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#2196F3;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1976D2;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="iconGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FFFFFF;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#E3F2FD;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景圆角矩形 -->
  <rect width="1024" height="1024" rx="180" ry="180" fill="url(#bgGradient)"/>
  
  <!-- 主图标 - Flutter风格 -->
  <g transform="translate(512, 512)">
    <!-- 上三角 -->
    <path d="M0 -200L300 100L150 250L0 100L-150 250L-300 100L0 -200Z" fill="url(#iconGradient)"/>
    <!-- 下三角 -->
    <path d="M0 50L150 200L0 350L-150 200L0 50Z" fill="#BBDEFB"/>
    
    <!-- 中心圆点 -->
    <circle cx="0" cy="0" r="30" fill="#FFFFFF"/>
  </g>
  
  <!-- 装饰元素 -->
  <circle cx="200" cy="200" r="20" fill="#FFFFFF" opacity="0.3"/>
  <circle cx="824" cy="824" r="15" fill="#FFFFFF" opacity="0.2"/>
  <circle cx="150" cy="800" r="25" fill="#FFFFFF" opacity="0.25"/>
  <circle cx="850" cy="180" r="18" fill="#FFFFFF" opacity="0.3"/>
</svg>
