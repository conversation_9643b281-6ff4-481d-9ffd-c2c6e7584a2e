# 图标和启动图设置指南

## 概述
本项目已配置了flutter_native_splash和flutter_launcher_icons插件，用于生成启动图和应用图标。

## 当前状态
- ✅ 已添加相关依赖到pubspec.yaml
- ✅ 已配置flutter_native_splash和flutter_launcher_icons
- ✅ 已创建SVG格式的图标文件
- ⚠️ 需要将SVG转换为PNG格式

## 需要完成的步骤

### 1. 转换图标格式
由于Flutter插件需要PNG格式，请将以下SVG文件转换为PNG：

**启动图标：**
- `assets/images/splash_logo.svg` → `assets/images/splash_logo.png` (288x288px)
- `assets/images/splash_logo_dark.svg` → `assets/images/splash_logo_dark.png` (288x288px)

**应用图标：**
- `assets/icons/app_icon.svg` → `assets/icons/app_icon.png` (1024x1024px)

### 2. 转换工具推荐
可以使用以下工具进行转换：
- **在线工具**: convertio.co, cloudconvert.com
- **设计软件**: Figma, Adobe Illustrator, Sketch
- **命令行工具**: ImageMagick, Inkscape

### 3. 生成启动图
转换完成后，运行以下命令生成启动图：
```bash
flutter packages pub run flutter_native_splash:create
```

### 4. 生成应用图标
运行以下命令生成各平台的应用图标：
```bash
flutter packages pub run flutter_launcher_icons:main
```

## 配置说明

### flutter_native_splash配置
```yaml
flutter_native_splash:
  color: "#ffffff"                              # 浅色模式背景色
  image: assets/images/splash_logo.png          # 浅色模式图标
  color_dark: "#000000"                         # 深色模式背景色
  image_dark: assets/images/splash_logo_dark.png # 深色模式图标
  android_12:                                   # Android 12+ 配置
    image: assets/images/splash_logo.png
    icon_background_color: "#ffffff"
    image_dark: assets/images/splash_logo_dark.png
    icon_background_color_dark: "#000000"
  web: false                                    # 不为Web生成启动图
```

### flutter_launcher_icons配置
```yaml
flutter_launcher_icons:
  android: "launcher_icon"                      # Android图标名称
  ios: true                                     # 生成iOS图标
  image_path: "assets/icons/app_icon.png"       # 图标路径
  min_sdk_android: 21                          # 最小Android SDK版本
  web:                                          # Web平台配置
    generate: true
    image_path: "assets/icons/app_icon.png"
  windows:                                      # Windows平台配置
    generate: true
    image_path: "assets/icons/app_icon.png"
    icon_size: 48
  macos:                                        # macOS平台配置
    generate: true
    image_path: "assets/icons/app_icon.png"
```

## 设计规范

### 启动图标
- **尺寸**: 288x288px (推荐)
- **格式**: PNG，背景透明
- **设计**: 简洁明了，适合在启动时短暂显示
- **颜色**: 提供浅色和深色两个版本

### 应用图标
- **尺寸**: 1024x1024px (高分辨率)
- **格式**: PNG
- **设计**: 符合各平台设计规范
- **注意**: iOS会自动添加圆角，Android可以是方形

## 注意事项

1. **图标尺寸**: 使用高分辨率图标确保在各种设备上都清晰
2. **背景处理**: 启动图标建议背景透明，应用图标可以有背景
3. **平台适配**: 不同平台对图标有不同要求，插件会自动处理
4. **测试**: 生成后在不同设备和主题下测试效果

## 故障排除

如果遇到问题：
1. 确保图片路径正确
2. 检查图片格式和尺寸
3. 清理项目：`flutter clean && flutter pub get`
4. 重新生成：重新运行生成命令

## 自定义配置

如需修改配置，编辑pubspec.yaml中的相应部分，然后重新运行生成命令。
